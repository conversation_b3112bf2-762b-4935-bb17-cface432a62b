{"$schema": "https://json.schemastore.org/package.json", "name": "opencode", "private": true, "type": "module", "packageManager": "bun@1.2.14", "scripts": {"dev": "bun run --conditions=development packages/opencode/src/index.ts", "typecheck": "bun run --filter='*' typecheck", "stainless": "./scripts/stainless", "postinstall": "./script/hooks"}, "workspaces": {"packages": ["packages/*", "packages/sdk/js"], "catalog": {"@types/node": "22.13.9", "@tsconfig/node22": "22.0.2", "ai": "5.0.0-beta.34", "hono": "4.7.10", "typescript": "5.8.2", "zod": "3.25.49", "remeda": "2.26.0"}}, "devDependencies": {"prettier": "3.5.3", "sst": "3.17.8"}, "repository": {"type": "git", "url": "https://github.com/sst/opencode"}, "license": "MIT", "prettier": {"semi": false, "printWidth": 120}, "trustedDependencies": ["esbuild", "protobufjs", "sharp"], "patchedDependencies": {}}