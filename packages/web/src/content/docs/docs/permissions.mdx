---
title: Permissions
description: Control what agents can do in your codebase.
---

By default, opencode **allows all operations** without requiring explicit approval.

The permissions system provides granular control to restrict what actions AI agents can perform in your codebase, allowing you to configure explicit approval requirements for sensitive operations like file editing, bash commands, and more.

---

## Configure

Permissions are configured in your `opencode.json` file under the `permission` key. Here are the available options.

---

### edit

Use the `permission.edit` key to control whether file editing operations require user approval.

- `"ask"` - Prompt for approval before editing files
- `"allow"` - Allow all file editing operations without approval
- `"deny"` - Make all file editing tools disabled and unavailable

```json title="opencode.json" {4}
{
  "$schema": "https://opencode.ai/config.json",
  "permission": {
    "edit": "ask"
  }
}
```

---

### bash

Controls whether bash commands require user approval.

:::tip
You can specify which commands you want to have run without approval.
:::

This can be configured globally or with specific patterns. Setting this to `"ask"`, requiring approval for all bash commands.
Setting this to `"deny"` is the strictest option, blocking LL<PERSON> from running that command or command pattern.

For example.

- **Ask for approval for all commands**

  ```json title="opencode.json"
  {
    "$schema": "https://opencode.ai/config.json",
    "permission": {
      "bash": "ask"
    }
  }
  ```

- **Disable all Terraform commands**

  ```json title="opencode.json"
  {
    "$schema": "https://opencode.ai/config.json",
    "permission": {
      "bash": {
        "terraform *": "deny"
      }
    }
  }
  ```

- **Approve specific commands**

  ```json title="opencode.json"
  {
    "$schema": "https://opencode.ai/config.json",
    "permission": {
      "bash": {
        "git status": "allow",
        "git diff": "allow",
        "npm run build": "allow",
        "ls": "allow",
        "pwd": "allow"
      }
    }
  }
  ```

- **Use wildcard patterns to restrict specific commands**

  ```json title="opencode.json"
  {
    "$schema": "https://opencode.ai/config.json",
    "permission": {
      "bash": {
        "git push": "ask",
        "*": "allow"
      }
    }
  }
  ```

  This configuration allows all commands by default (`"*": "allow"`) but requires approval for `git push` commands.
