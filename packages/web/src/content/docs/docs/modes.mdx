---
title: Modes
description: Different modes for different use cases.
---

Modes in opencode allow you to customize the behavior, tools, and prompts for different use cases.

It comes with two built-in modes: **build** and **plan**. You can customize
these or configure your own through the opencode config.

:::tip
Use the plan mode to analyze code and review suggestions without making any code
changes.
:::

You can switch between modes during a session or configure them in your config file.

---

## Built-in

opencode comes with two built-in modes.

---

### Build

Build is the **default** mode with all tools enabled. This is the standard mode for development work where you need full access to file operations and system commands.

---

### Plan

A restricted mode designed for planning and analysis. In plan mode, the following tools are disabled by default:

- `write` - Cannot create new files
- `edit` - Cannot modify existing files
- `patch` - Cannot apply patches
- `bash` - Cannot execute shell commands

This mode is useful when you want the AI to analyze code, suggest changes, or create plans without making any actual modifications to your codebase.

---

## Switching

You can switch between modes during a session using the _Tab_ key. Or your configured `switch_mode` keybind.

See also: [Formatters](/docs/formatters) for information about code formatting configuration.

---

## Configure

You can customize the built-in modes or create your own through configuration. Modes can be configured in two ways:

### JSON Configuration

Configure modes in your `opencode.json` config file:

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "mode": {
    "build": {
      "model": "anthropic/claude-sonnet-4-20250514",
      "prompt": "{file:./prompts/build.txt}",
      "tools": {
        "write": true,
        "edit": true,
        "bash": true
      }
    },
    "plan": {
      "model": "anthropic/claude-haiku-4-20250514",
      "tools": {
        "write": false,
        "edit": false,
        "bash": false
      }
    }
  }
}
```

### Markdown Configuration

You can also define modes using markdown files. Place them in:

- Global: `~/.config/opencode/mode/`
- Project: `.opencode/mode/`

```markdown title="~/.config/opencode/mode/review.md"
---
model: anthropic/claude-sonnet-4-20250514
temperature: 0.1
tools:
  write: false
  edit: false
  bash: false
---

You are in code review mode. Focus on:

- Code quality and best practices
- Potential bugs and edge cases
- Performance implications
- Security considerations

Provide constructive feedback without making direct changes.
```

The markdown file name becomes the mode name (e.g., `review.md` creates a `review` mode).

Let's look at these configuration options in detail.

---

### Model

Use the `model` config to override the default model for this mode. Useful for using different models optimized for different tasks. For example, a faster model for planning, a more capable model for implementation.

```json title="opencode.json"
{
  "mode": {
    "plan": {
      "model": "anthropic/claude-haiku-4-20250514"
    }
  }
}
```

---

### Temperature

Control the randomness and creativity of the AI's responses with the `temperature` config. Lower values make responses more focused and deterministic, while higher values increase creativity and variability.

```json title="opencode.json"
{
  "mode": {
    "plan": {
      "temperature": 0.1
    },
    "creative": {
      "temperature": 0.8
    }
  }
}
```

Temperature values typically range from 0.0 to 1.0:

- **0.0-0.2**: Very focused and deterministic responses, ideal for code analysis and planning
- **0.3-0.5**: Balanced responses with some creativity, good for general development tasks
- **0.6-1.0**: More creative and varied responses, useful for brainstorming and exploration

```json title="opencode.json"
{
  "mode": {
    "analyze": {
      "temperature": 0.1,
      "prompt": "{file:./prompts/analysis.txt}"
    },
    "build": {
      "temperature": 0.3
    },
    "brainstorm": {
      "temperature": 0.7,
      "prompt": "{file:./prompts/creative.txt}"
    }
  }
}
```

If no temperature is specified, opencode uses model-specific defaults (typically 0 for most models, 0.55 for Qwen models).

---

### Prompt

Specify a custom system prompt file for this mode with the `prompt` config. The prompt file should contain instructions specific to the mode's purpose.

```json title="opencode.json"
{
  "mode": {
    "review": {
      "prompt": "{file:./prompts/code-review.txt}"
    }
  }
}
```

This path is relative to where the config file is located. So this works for
both the global opencode config and the project specific config.

---

### Tools

Control which tools are available in this mode with the `tools` config. You can enable or disable specific tools by setting them to `true` or `false`.

```json
{
  "mode": {
    "readonly": {
      "tools": {
        "write": false,
        "edit": false,
        "bash": false,
        "read": true,
        "grep": true,
        "glob": true
      }
    }
  }
}
```

If no tools are specified, all tools are enabled by default.

---

#### Available tools

Here are all the tools can be controlled through the mode config.

| Tool        | Description             |
| ----------- | ----------------------- |
| `bash`      | Execute shell commands  |
| `edit`      | Modify existing files   |
| `write`     | Create new files        |
| `read`      | Read file contents      |
| `grep`      | Search file contents    |
| `glob`      | Find files by pattern   |
| `list`      | List directory contents |
| `patch`     | Apply patches to files  |
| `todowrite` | Manage todo lists       |
| `todoread`  | Read todo lists         |
| `webfetch`  | Fetch web content       |

---

## Custom modes

You can create your own custom modes by adding them to the configuration. Here are examples using both approaches:

### Using JSON configuration

```json title="opencode.json" {4-14}
{
  "$schema": "https://opencode.ai/config.json",
  "mode": {
    "docs": {
      "prompt": "{file:./prompts/documentation.txt}",
      "tools": {
        "write": true,
        "edit": true,
        "bash": false,
        "read": true,
        "grep": true,
        "glob": true
      }
    }
  }
}
```

### Using markdown files

Create mode files in `.opencode/mode/` for project-specific modes or `~/.config/opencode/mode/` for global modes:

```markdown title=".opencode/mode/debug.md"
---
temperature: 0.1
tools:
  bash: true
  read: true
  grep: true
  write: false
  edit: false
---

You are in debug mode. Your primary goal is to help investigate and diagnose issues.

Focus on:

- Understanding the problem through careful analysis
- Using bash commands to inspect system state
- Reading relevant files and logs
- Searching for patterns and anomalies
- Providing clear explanations of findings

Do not make any changes to files. Only investigate and report.
```

```markdown title="~/.config/opencode/mode/refactor.md"
---
model: anthropic/claude-sonnet-4-20250514
temperature: 0.2
tools:
  edit: true
  read: true
  grep: true
  glob: true
---

You are in refactoring mode. Focus on improving code quality without changing functionality.

Priorities:

- Improve code readability and maintainability
- Apply consistent naming conventions
- Reduce code duplication
- Optimize performance where appropriate
- Ensure all tests continue to pass
```

---

### Use cases

Here are some common use cases for different modes.

- **Build mode**: Full development work with all tools enabled
- **Plan mode**: Analysis and planning without making changes
- **Review mode**: Code review with read-only access plus documentation tools
- **Debug mode**: Focused on investigation with bash and read tools enabled
- **Docs mode**: Documentation writing with file operations but no system commands

You might also find different models are good for different use cases.
