{"$schema": "https://json.schemastore.org/package.json", "name": "@opencode-ai/plugin", "version": "0.3.133", "type": "module", "scripts": {"typecheck": "tsc --noEmit"}, "exports": {".": {"development": "./src/index.ts", "import": "./dist/index.js"}}, "files": ["dist"], "dependencies": {"@opencode-ai/sdk": "workspace:*"}, "devDependencies": {"typescript": "catalog:", "@hey-api/openapi-ts": "0.80.1", "@tsconfig/node22": "catalog:"}}